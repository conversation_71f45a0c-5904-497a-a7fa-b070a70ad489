use aqsoc;

CREATE TABLE `tbl_device_config` (
                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                     `device_name` varchar(50) DEFAULT NULL COMMENT '设备名称',
                                     `device_ip` varchar(30) DEFAULT NULL COMMENT '设备IP',
                                     `device_params` text COMMENT '设备接入参数',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
                                     `status` int DEFAULT '1' COMMENT '状态',
                                     `filter_log_last_time` datetime DEFAULT NULL COMMENT '封禁日志最后更新时间',
                                     `alarm_detail_last_time` datetime DEFAULT NULL COMMENT '流量告警日志最后更新时间',
                                     `risk_asset_last_time` datetime DEFAULT NULL COMMENT '风险资产最后更新时间',
                                     `is_default` bit(1) DEFAULT b'0' COMMENT '是否默认配置',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备接入配置';

ALTER TABLE ffsafe_ipfilter_blocking
    ADD COLUMN device_config_id bigint COMMENT '设备配置ID';

ALTER TABLE ffsafe_flow_detail
    ADD COLUMN device_config_id bigint COMMENT '设备配置ID';

ALTER TABLE ffsafe_flow_risk_assets
    ADD COLUMN device_config_id bigint COMMENT '设备配置ID';

ALTER TABLE sys_job
    ADD COLUMN device_config_id bigint COMMENT '设备配置ID';

ALTER TABLE ffsafe_ipfilter_log
    ADD COLUMN device_config_id bigint COMMENT '设备配置ID';

ALTER TABLE tbl_network_domain
    ADD COLUMN device_config_id bigint COMMENT '设备配置ID';

ALTER TABLE tbl_threaten_alarm
    ADD COLUMN device_config_id bigint COMMENT '设备配置ID';

ALTER TABLE tbl_honeypot_alarm
    ADD COLUMN device_config_id bigint COMMENT '设备配置ID';


-- =====================================================
-- 创建漏扫报告记录表
-- =====================================================
CREATE TABLE `ffsafe_scan_report_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `generate_time` datetime NULL COMMENT '生成时间',
  `generate_source` int NOT NULL COMMENT '生成入口：1=单条生成，2=批量生成',
  `report_type` int NOT NULL COMMENT '报表类型 2: 主机漏扫报表  1: web漏扫报表',
  `report_id` int NULL COMMENT '报表ID',
  `report_status` tinyint NULL COMMENT '报表状态: 0: 生成中  1: 生成完毕  2: 下载完毕',
  `file_name` varchar(200) NULL COMMENT '报表文件名',
  `down_name` varchar(200) NULL COMMENT '报表下载名',
  `report_percent` tinyint NULL COMMENT '报表进度 0-100',
  `minio_path` varchar(500) NULL COMMENT 'minio 路径',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_report_status` (`report_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='漏扫报告记录表';

-- =====================================================
-- 扫描报告与任务关联表
-- =====================================================
CREATE TABLE `ffsafe_scan_report_task_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `scan_report_record_id` bigint NOT NULL COMMENT '扫描报告记录ID',
  `task_summary_id` int NOT NULL COMMENT '扫描任务汇总ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_report_task` (`scan_report_record_id`,`task_summary_id`),
  KEY `idx_scan_report_record_id` (`scan_report_record_id`),
  KEY `idx_task_summary_id` (`task_summary_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='扫描报告与任务关联表';


-- 漏扫明细添加summary_id字段关联
ALTER TABLE ffsafe_hostscan_taskresult ADD COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_hostscan_vulnresult ADD COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_hostscan_portresult ADD COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_hostscan_wpresult ADD COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_webscan_vulnresult ADD COLUMN summary_id INT NULL COMMENT '汇总记录ID';