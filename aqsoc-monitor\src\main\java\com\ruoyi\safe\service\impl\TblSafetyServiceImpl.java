package com.ruoyi.safe.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.common.utils.CollectionUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.bean.BeanUtil;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.safe.domain.MonitorHandle;
import com.ruoyi.safe.domain.TblAssetOverview;
import com.ruoyi.safe.domain.TblNetworkIpMac;
import com.ruoyi.safe.domain.TblSafety;
import com.ruoyi.safe.domain.dto.QueryDeptSafetyCountDto;
import com.ruoyi.safe.dto.TblSafetyImportDTO;
import com.ruoyi.safe.mapper.MonitorHandleMapper;
import com.ruoyi.safe.mapper.TblSafetyMapper;
import com.ruoyi.safe.service.*;
import com.ruoyi.safe.task.AssetOnlineTask;
import com.ruoyi.safe.vo.DeptSafetyCount;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.dict.domain.TblLocation;
import com.ruoyi.dict.service.ITblLocationService;
import com.ruoyi.safe.domain.TblVendor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.web.util.matcher.IpAddressMatcher;
import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 安全设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
@Service
public class TblSafetyServiceImpl implements ITblSafetyService
{
    @Autowired
    private TblSafetyMapper tblSafetyMapper;
    @Autowired
    private ITblAssetOverviewService tblAssetOverviewService;
    @Autowired
    private INetworkDomainService networkDomainService;
    @Autowired
    private ITblNetworkIpMacService tblNetworkIpMacService;
    @Autowired
    private Snowflake snowflake;
    @Autowired
    private MonitorHandleMapper monitorHandleMapper;
    @Autowired
    private ITblFirewallPolicyService firewallPolicyService;
    @Autowired
    private ITblFirewallNatService firewallNatService;
    @Autowired
    private AssetOnlineTask assetOnlineTask;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private ITblLocationService tblLocationService;
    @Autowired
    private ITblVendorService tblVendorService;

    private static final Logger log = LoggerFactory.getLogger(TblSafetyServiceImpl.class);

    /**
     * 查询安全设备
     *
     * @param assetId 安全设备主键
     * @return 安全设备
     */
    @Override
    public TblSafety selectTblSafetyByAssetId(Long assetId)
    {
        TblSafety tblSafety = tblSafetyMapper.selectTblSafetyByAssetId(assetId);
        if(ObjectUtils.isNotEmpty(tblSafety)){
            TblAssetOverview tblAssetOverview = tblAssetOverviewService.selectInfoByAssetId(assetId);
            TblNetworkIpMac net = new TblNetworkIpMac();
            net.setMainIp("1");
            net.setAssetId(assetId);
            List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(net);
            if (tblSafety != null && tblAssetOverview != null) {
                BeanUtil.copyPropertiesIgnoreNull(tblAssetOverview, tblSafety);
                if (ObjectUtils.isNotEmpty(tblNetworkIpMacs)) {
                    tblSafety.setIp(tblNetworkIpMacs.get(0).getIpv4());
                    tblSafety.setMac(tblNetworkIpMacs.get(0).getMac());
                    tblSafety.setDomainId(tblNetworkIpMacs.get(0).getDomainId());
                    tblSafety.setDomainName(tblNetworkIpMacs.get(0).getDomainName());
                }
            }
            if(ObjectUtils.isNotEmpty(tblAssetOverview)){
                NetworkDomain tblDomain = networkDomainService.selectNetworkDomainByDomainId(tblAssetOverview.getDomainId());
                if (ObjectUtils.isNotEmpty(tblDomain)) {
                    tblSafety.setDomainName(tblDomain.getDomainName());
                }
            }
        }


        return tblSafety;
    }

    /**
     * 查询安全设备列表
     *
     * @param assetIds 安全设备主键
     * @return 安全设备集合
     */
    @Override
    public List<TblSafety> selectTblSafetyByAssetIds(Long[] assetIds)
    {
        List<TblSafety> assetList = tblSafetyMapper.selectTblSafetyByAssetIds(assetIds);
        List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewService.selectInfoByAssetIds(assetIds);
        CollectionUtils.associationOneToOne(
                assetList,
                tblAssetOverviews,
                TblSafety::getAssetId,
                TblAssetOverview::getAssetId,
                (asset, assetOverview) -> {
                    BeanUtil.copyPropertiesIgnoreNull(assetOverview, asset);
                }
        );
        Optional.ofNullable(assetList.stream().map(asset -> asset.getDomainId()).toArray(Long[]::new))
                .map(ids -> {
                    if (ObjectUtils.isNotEmpty(ids))
                        return networkDomainService.selectNetworkDomainByDomainIds(ids, new NetworkDomain());
                    return null;
                })
                .ifPresent(domainList -> {
                    CollectionUtils.associationOneToOne(
                            assetList, domainList,
                            TblSafety::getDomainId, NetworkDomain::getDomainId,
                            (asset, domain) -> {
                                asset.setDomainName(domain.getDomainName());
                            }
                    );
                });
        return assetList;
    }

    /**
     * 查询安全设备列表
     *
     * @param tblSafety 安全设备
     * @return 安全设备
     */
    @Override
    public List<TblSafety> selectTblSafetyList(TblSafety tblSafety)
    {
        List<TblSafety> assetList = tblSafetyMapper.selectTblSafetyList(tblSafety);

        return assetList;
    }

    /**
     * 新增安全设备
     *
     * @param tblSafety 安全设备
     * @return 结果
     */
    @Override
    public int insertTblSafety(TblSafety tblSafety)
    {
        if (tblSafety.getHandleId()!=null){
            monitorHandleMapper.deleteMonitorHandleById(tblSafety.getHandleId());
        }
        tblSafety.setAssetId(snowflake.nextId());
        tblSafety.setCreateTime(DateUtils.getNowDate());
        if(ObjectUtils.isEmpty(tblSafety.getAssetClass())){
            tblSafety.setAssetClass(3L);
        }
        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
        tblNetworkIpMac.setAssetId(tblSafety.getAssetId());
        tblNetworkIpMac.setMainIp("1");
        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
        if(ObjectUtils.isNotEmpty(tblSafety.getIp())||ObjectUtils.isNotEmpty(tblSafety.getDomainId())){
            if(ObjectUtils.isNotEmpty(tblNetworkIpMacs)){
                TblNetworkIpMac mainIp = tblNetworkIpMacs.get(0);
                mainIp.setIpv4(tblSafety.getIp());
                mainIp.setDomainId(tblSafety.getDomainId());
                mainIp.setMac(tblSafety.getMac());
                tblNetworkIpMacService.updateTblNetworkIpMac(mainIp);
            }else{
                tblNetworkIpMac.setIpv4(tblSafety.getIp());
                tblNetworkIpMac.setDomainId(tblSafety.getDomainId());
                tblNetworkIpMac.setMac(tblSafety.getMac());
                tblNetworkIpMacService.insertTblNetworkIpMac(tblNetworkIpMac);
            }
        }else{
            tblNetworkIpMac.setDomainId(tblSafety.getDomainId());
            tblNetworkIpMacService.updateTblNetworkIpMac(tblNetworkIpMac);
        }
        tblAssetOverviewService.insertTblAssetOverview(tblSafety);
        int i = tblSafetyMapper.insertTblSafety(tblSafety);
        //在线检测
        if(StrUtil.isNotBlank(tblSafety.getIp())){
            List<JSONObject> assetList = new ArrayList<>(1);
            JSONObject assetInfo = new JSONObject();
            assetInfo.put("ip",tblSafety.getIp());
            assetInfo.put("assetId",tblSafety.getAssetId());
            assetList.add(assetInfo);
            assetOnlineTask.asyncAssetOnlineCheck(assetList);
        }
        return i;
    }

    /**
     * 修改安全设备
     *
     * @param tblSafety 安全设备
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateTblSafety(TblSafety tblSafety)
    {
        TblSafety old = selectTblSafetyByAssetId(tblSafety.getAssetId());
        if (tblSafety.getHandleId()!=null){
            MonitorHandle monitorHandle=new MonitorHandle();
            monitorHandle.setId(tblSafety.getHandleId());
            monitorHandle.setIsDel(1);
            monitorHandleMapper.updateMonitorHandle(monitorHandle);
        }
        tblSafety.setUpdateTime(DateUtils.getNowDate());
        if(ObjectUtils.isEmpty(tblSafety.getAssetClass())){
            tblSafety.setAssetClass(3L);
        }
        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
        tblNetworkIpMac.setAssetId(tblSafety.getAssetId());
        tblNetworkIpMac.setMainIp("1");
        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
        if(ObjectUtils.isNotEmpty(tblSafety.getIp())||ObjectUtils.isNotEmpty(tblSafety.getDomainId())){
            if(ObjectUtils.isNotEmpty(tblNetworkIpMacs)){
                TblNetworkIpMac mainIp = tblNetworkIpMacs.get(0);
                mainIp.setIpv4(tblSafety.getIp());
                mainIp.setDomainId(tblSafety.getDomainId());
                mainIp.setMac(tblSafety.getMac());
                tblNetworkIpMacService.updateTblNetworkIpMac(mainIp);
            }else{
                tblNetworkIpMac.setIpv4(tblSafety.getIp());
                tblNetworkIpMac.setDomainId(tblSafety.getDomainId());
                tblNetworkIpMac.setMac(tblSafety.getMac());
                tblNetworkIpMacService.insertTblNetworkIpMac(tblNetworkIpMac);
            }
        }else{
            tblNetworkIpMac.setDomainId(tblSafety.getDomainId());
            tblNetworkIpMacService.updateTblNetworkIpMac(tblNetworkIpMac);
        }
        tblAssetOverviewService.updateTblAssetOverview(tblSafety);
        int i = tblSafetyMapper.updateTblSafety(tblSafety);
        if(old != null){
            String assetTypeDesc = tblSafety.getAssetTypeDesc();
            if(StrUtil.isNotBlank(assetTypeDesc) && !Objects.equals(tblSafety.getAssetType(), old.getAssetType()) && !assetTypeDesc.contains("防火墙")){
                //清空策略、暴露面
                firewallPolicyService.deleteTblFirewallPolicyByAssetIds(Collections.singletonList(tblSafety.getAssetId()));
                firewallNatService.deleteTblFirewallNatByAssetIds(Collections.singletonList(tblSafety.getAssetId()));
            }
        }
        //在线检测
        if(StrUtil.isNotBlank(tblSafety.getIp())){
            List<JSONObject> assetList = new ArrayList<>(1);
            JSONObject assetInfo = new JSONObject();
            assetInfo.put("ip",tblSafety.getIp());
            assetInfo.put("assetId",tblSafety.getAssetId());
            assetList.add(assetInfo);
            assetOnlineTask.asyncAssetOnlineCheck(assetList);
        }
        return i;
    }

    /**
     * 批量删除安全设备
     *
     * @param assetIds 需要删除的安全设备主键
     * @return 结果
     */
    @Override
    public int deleteTblSafetyByAssetIds(Long[] assetIds)
    {
        tblAssetOverviewService.deleteTblAssetOverviewByIds(assetIds);
        return tblSafetyMapper.deleteTblSafetyByAssetIds(assetIds);
    }

    /**
     * 删除安全设备信息
     *
     * @param assetId 安全设备主键
     * @return 结果
     */
    @Override
    public int deleteTblSafetyByAssetId(Long assetId)
    {
        tblAssetOverviewService.deleteTblAssetOverviewById(assetId);
        return tblSafetyMapper.deleteTblSafetyByAssetId(assetId);
    }

    /**
     * 资产选择通用组件查询
     */
    @Override
    public List<TblSafety> assetSelectBySafety(HashMap<String, String> params) {
        List<TblSafety> tblSafetys = tblSafetyMapper.assetSelectBySafety(params);
        tblSafetys.forEach(server -> server.setAssetType(2L));
        return tblSafetys;
    }
    @Override
    public List<TblSafety> assetSelectBySafety2(HashMap<String, String> params) {
        List<TblSafety> tblSafetys = tblSafetyMapper.assetSelectBySafety2(params);
        tblSafetys.forEach(server -> server.setAssetType(2L));
        return tblSafetys;
    }

    @Override
    public List<JSONObject> selectTblSafetyLocationIdIsNotNull() {
        return tblSafetyMapper.selectTblSafetyLocationIdIsNotNull();
    }

    @Override
    public List<TblSafety> selectFirewallListByIps(List<String> firewallIps) {
        return tblSafetyMapper.selectFirewallListByIps(firewallIps);
    }

    @Override
    public int countNum() {
        return tblSafetyMapper.countNum();
    }

    @Override
    public List<TreeSelect> getDeptSafetyCount(QueryDeptSafetyCountDto queryCountDto) {
        queryCountDto.getSysDept().setDeptId(null);
        // 获取部门树列表
        List<TreeSelect> treeSelects = deptService.selectDeptTreeList(queryCountDto.getSysDept());
        // 获取部门ID列表
        List<Long> deptIdList = getDeptIdList(treeSelects);
        if (CollUtil.isNotEmpty(deptIdList)) {
            queryCountDto.setDeptIdList(deptIdList);
            List<DeptSafetyCount> deptSafetyCounts = tblSafetyMapper.getDeptSafetyCount(queryCountDto);
            // 创建部门ID与安全设备计数的映射
            Map<Long, DeptSafetyCount> safetyCountMap = deptSafetyCounts.stream()
                    .collect(Collectors.toMap(DeptSafetyCount::getDeptId, Function.identity()));
            // 计算祖先安全设备计数
            Map<Long, DeptSafetyCount> updatedSafetyCountMap = calculateAncestorSafetyCounts(safetyCountMap);
            // 应用并排序安全设备计数
            applyAndSortSafetyCounts(treeSelects, updatedSafetyCountMap);
        }
        return treeSelects;
    }

    /**
     * 计算祖先安全设备计数
     *
     * @param safetyCountMap
     * @return
     */
    private Map<Long, DeptSafetyCount> calculateAncestorSafetyCounts(Map<Long, DeptSafetyCount> safetyCountMap) {
        Map<Long, Integer> ancestorCounts = new HashMap<>();
        // Step 1: 计算每个祖先的安全设备计数总和
        for (DeptSafetyCount dept : safetyCountMap.values()) {
            String[] ancestors = dept.getAncestors().split(",");
            for (String ancestorId : ancestors) {
                if (NumberUtil.isLong(ancestorId)) {
                    ancestorCounts.merge(Long.parseLong(ancestorId), dept.getSafetyCount(), Integer::sum);
                }
            }
        }
        // Step 2: 使用为每个祖先计算的总和更新安全设备计数
        return safetyCountMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> new DeptSafetyCount(entry.getKey(), entry.getValue().getAncestors(),
                                ancestorCounts.getOrDefault(entry.getKey(), 0) + entry.getValue().getSafetyCount())));
    }

    /**
     * 应用并排序安全设备计数
     */
    private void applyAndSortSafetyCounts(List<TreeSelect> treeSelects, Map<Long, DeptSafetyCount> safetyCountMap) {
        for (TreeSelect treeSelect : treeSelects) {
            DeptSafetyCount safetyCount = safetyCountMap.get(treeSelect.getId());
            if (safetyCount != null) {
                int count = safetyCount.getSafetyCount() != null ? safetyCount.getSafetyCount() : 0;
                treeSelect.setCount(count);
            }
            List<TreeSelect> children = treeSelect.getChildren();
            if (CollUtil.isNotEmpty(children)) {
                // 按安全设备计数降序对子项进行排序
                children.sort(Comparator.comparing((TreeSelect child) -> {
                    DeptSafetyCount count = safetyCountMap.getOrDefault(child.getId(), new DeptSafetyCount(child.getId(), "", 0));
                    return count.getSafetyCount();
                }).reversed().thenComparing(TreeSelect::getId));
                // 递归处理子项
                applyAndSortSafetyCounts(children, safetyCountMap);
            }
        }
    }

    /**
     * 获取部门ID列表
     *
     * @param treeSelects
     * @return
     */
    private List<Long> getDeptIdList(List<TreeSelect> treeSelects) {
        List<Long> deptIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(treeSelects)) {
            recursiveGetDeptIds(treeSelects, deptIds);
        }
        return deptIds;
    }

    /**
     * 递归获取部门ID列表
     *
     * @param nodes
     * @param deptIds
     */
    private void recursiveGetDeptIds(List<TreeSelect> nodes, List<Long> deptIds) {
        for (TreeSelect node : nodes) {
            deptIds.add(node.getId());
            if (CollUtil.isNotEmpty(node.getChildren())) {
                recursiveGetDeptIds(node.getChildren(), deptIds);
            }
        }
    }

    /**
     * 导入安全设备数据（基于简化模板）
     * 采用两阶段提交模式确保事务原子性：
     * 第一阶段：完整数据验证和错误收集
     * 第二阶段：批量数据保存
     *
     * @param dtoList TblSafetyImportDTO列表
     * @return 导入结果信息
     * @throws Exception 导入异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importSafetyFromTemplate(List<TblSafetyImportDTO> dtoList) throws Exception {
        log.info("开始导入安全设备数据，数据量：{}", dtoList != null ? dtoList.size() : 0);

        // 1. 数据验证：空值检查、数量限制
        if (dtoList == null || dtoList.isEmpty()) {
            throw new ServiceException("导入安全设备数据不能为空！");
        }
        if (dtoList.size() > 500) {
            throw new ServiceException("最多支持500条数据导入！");
        }

        // 2. 初始化统计变量和错误收集器
        int successNum = 0;
        int failureNum = 0;
        List<String> errorMessages = new ArrayList<>();
        StringBuilder successMsg = new StringBuilder();

        try {
            // 3. 预加载字典数据（批量查询优化）
            log.info("开始批量查询字典数据...");
            Map<String, SysDept> deptMap = prepareDeptMap();
            Map<String, SysDictData> imptGradeMap = prepareImptGradeMap();
            Map<String, TblLocation> locationMap = prepareLocationMap();
            Map<String, Long> vendorMap = prepareVendorMap();
            List<NetworkDomain> domains = prepareNetworkDomains();
            Map<String, NetworkDomain> domainMap = networkDomainService.selectNetworkDomainList(new NetworkDomain())
                    .stream().collect(Collectors.toMap(NetworkDomain::getDomainName, domain -> domain));
            log.info("字典数据查询完成");

            // 4. 批量生成资产编码
            log.info("开始批量生成资产编码，数量：{}", dtoList.size());
            List<String> preGeneratedAssetCodes = batchGenerateSafetyCodes(dtoList.size());
            log.info("资产编码批量生成完成");

            // 5. 第一阶段：数据验证和转换
            log.info("开始第一阶段：数据验证和转换，数据量：{}", dtoList.size());
            List<TblSafety> validSafeties = new ArrayList<>();

            for (int i = 0; i < dtoList.size(); i++) {
                TblSafetyImportDTO dto = dtoList.get(i);
                int rowIndex = i;
                int currentRow = rowIndex + 3; // Excel行号（含表头）

                try {
                    log.debug("开始处理第{}行数据：资产名称={}, IP={}", currentRow, dto.getAssetName(), dto.getIp());

                    // 记录当前错误数量，用于判断本行是否有新错误
                    int errorCountBefore = errorMessages.size();

                    // 数据验证（收集错误，不立即返回）
                    // 1. 基础数据验证
                    validateSafetyData(dto, rowIndex, errorMessages, deptMap, imptGradeMap, locationMap);

                    // 2. 数据格式验证
                    validateDataFormats(dto, rowIndex, errorMessages);

                    // 3. 业务规则验证
                    validateBusinessRules(dto, rowIndex, errorMessages, vendorMap, domains);

                    // 检查本行是否有验证错误
                    int errorCountAfter = errorMessages.size();
                    boolean hasRowError = errorCountAfter > errorCountBefore;

                    if (!hasRowError) {
                        // 转换为TblSafety对象
                        String preGeneratedAssetCode = preGeneratedAssetCodes.get(i);
                        TblSafety safety = buildSafetyFromDto(dto, deptMap, imptGradeMap, locationMap, vendorMap, domains, preGeneratedAssetCode, domainMap);
                        if (safety != null) {
                            validSafeties.add(safety);
                            successNum++;
                            log.debug("第{}行数据转换成功：资产名称={}, 资产编码={}", currentRow, safety.getAssetName(), safety.getAssetCode());
                        } else {
                            failureNum++;
                            String errorMsg = "第" + currentRow + "行：数据转换失败，生成的安全设备对象为空";
                            errorMessages.add(errorMsg);
                            log.warn("第{}行数据转换失败：生成的安全设备对象为空", currentRow);
                        }
                    } else {
                        failureNum++;
                        log.warn("第{}行数据验证失败，跳过转换", currentRow);
                    }

                } catch (Exception e) {
                    failureNum++;
                    String errorMsg = "第" + currentRow + "行数据处理异常：" + e.getMessage();
                    errorMessages.add(errorMsg);
                    log.error("第{}行数据处理异常：资产名称={}, IP={}, 异常信息={}", currentRow, dto.getAssetName(), dto.getIp(), e.getMessage(), e);
                }
            }

            log.info("第一阶段完成，验证结果：成功{}条，失败{}条", successNum, failureNum);

            // 6. 检查是否有错误，如果有则抛出异常
            if (CollUtil.isNotEmpty(errorMessages)) {
                log.warn("数据验证阶段发现{}个错误，准备回滚事务", errorMessages.size());
                StringBuilder failureMsg = new StringBuilder();
                failureMsg.append("共").append(failureNum).append("条数据格式不正确，错误如下：\n");
                for (String error : errorMessages) {
                    failureMsg.append(error).append("\n");
                }

                // 记录详细的错误信息用于调试
                log.error("数据验证失败详情：\n{}", failureMsg.toString());
                throw new ServiceException(failureMsg.toString());
            }

            // 7. 第二阶段：批量保存数据
            log.info("开始第二阶段：批量保存数据，有效数据量：{}", validSafeties.size());
            if (CollUtil.isNotEmpty(validSafeties)) {
                batchSaveSafetyData(validSafeties);
                log.info("第二阶段完成：批量保存成功");
            } else {
                log.warn("没有有效数据需要保存");
            }

            // 8. 成功处理：返回成功信息和统计数据
            successMsg.append("数据导入成功！共").append(successNum).append("条，数据如下：\n");
            for (TblSafety safety : validSafeties) {
                successMsg.append("资产名称：").append(safety.getAssetName())
                         .append("，IP地址：").append(safety.getIp())
                         .append("，所属部门：").append(safety.getDeptName()).append("\n");
            }

            log.info("安全设备数据导入完成，成功：{}条，失败：{}条", successNum, failureNum);
            return successMsg.toString();

        } catch (ServiceException e) {
            log.error("安全设备数据导入业务异常：{}", e.getMessage());
            log.info("事务将自动回滚，已处理的数据不会保存到数据库");
            // 不要在此处重复添加"导入失败："前缀，交由控制器层统一包装
            throw e;
        } catch (Exception e) {
            log.error("安全设备数据导入系统异常：{}", e.getMessage(), e);
            log.info("事务将自动回滚，已处理的数据不会保存到数据库");
            throw new ServiceException("数据导入系统错误：" + e.getMessage());
        }
    }

    /**
     * 预加载部门数据
     */
    private Map<String, SysDept> prepareDeptMap() {
        return Optional.ofNullable(deptService.selectDeptList(new SysDept()))
                .orElse(Collections.emptyList())
                .stream()
                .filter(d -> StrUtil.isNotBlank(d.getDeptName()))
                .collect(Collectors.toMap(SysDept::getDeptName, d -> d, (a, b) -> b));
    }

    /**
     * 预加载重要程度字典数据
     */
    private Map<String, SysDictData> prepareImptGradeMap() {
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("impt_grade");
        return Optional.ofNullable(dictDataService.selectDictDataList(sysDictData))
                .orElse(Collections.emptyList())
                .stream()
                .filter(d -> StrUtil.isNotBlank(d.getDictLabel()))
                .collect(Collectors.toMap(SysDictData::getDictLabel, d -> d, (a, b) -> b));
    }

    /**
     * 预加载位置数据
     */
    private Map<String, TblLocation> prepareLocationMap() {
        return Optional.ofNullable(tblLocationService.selectTblLocationList(new TblLocation()))
                .orElse(Collections.emptyList())
                .stream()
                .filter(l -> StrUtil.isNotBlank(l.getLocationFullName()))
                .collect(Collectors.toMap(TblLocation::getLocationFullName, l -> l, (a, b) -> b));
    }

    /**
     * 预加载供应商数据
     */
    private Map<String, Long> prepareVendorMap() {
        return Optional.ofNullable(tblVendorService.selectTblVendorList(new TblVendor()))
                .orElse(Collections.emptyList())
                .stream()
                .filter(v -> StrUtil.isNotBlank(v.getVendorName()))
                .collect(Collectors.toMap(TblVendor::getVendorName, TblVendor::getId, (a, b) -> b));
    }

    /**
     * 预加载网络区域数据
     */
    private List<NetworkDomain> prepareNetworkDomains() {
        return Optional.ofNullable(networkDomainService.selectNetworkDomainList(new NetworkDomain()))
                .orElse(Collections.emptyList());
    }

    /**
     * 批量生成安全设备资产编码
     */
    private List<String> batchGenerateSafetyCodes(int count) {
        List<String> codes = new ArrayList<>();
        if (count <= 0) return codes;
        String ts = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String prefix = "AQSB" + ts;

        // 生成候选编码
        for (int i = 1; i <= count + 100; i++) {
            codes.add(prefix + String.format("%03d", i));
        }

        // 过滤已存在编码（简化实现，实际项目中应该查询数据库）
        List<String> result = new ArrayList<>();
        int idx = 1;
        while (result.size() < count) {
            String code = prefix + String.format("%03d", idx++);
            result.add(code);
            if (idx > 9999) {
                throw new ServiceException("资产编码生成失败：无法找到足够的可用编码");
            }
        }
        return result;
    }

    /**
     * 基础数据验证方法 - 错误收集模式
     * 负责必填字段验证、存在性验证、长度验证等基础数据检查
     * 将原有Controller层的立即返回错误模式改为错误收集模式
     * 确保能够收集所有数据的错误信息，而不是遇到第一个错误就停止
     * 注意：格式验证（如IP格式）由validateDataFormats方法负责
     */
    private void validateSafetyData(TblSafetyImportDTO dto, int rowIndex, List<String> errorMessages,
                                   Map<String, SysDept> deptMap, Map<String, SysDictData> imptGradeMap,
                                   Map<String, TblLocation> locationMap) {
        int row = rowIndex + 3; // Excel含表头，从第3行开始

        // 1. 必填字段验证（对应原Controller第371-383行）
        if (StrUtil.isBlank(dto.getAssetName())) {
            errorMessages.add("第" + row + "行：资产名称不能为空");
        }
        if (StrUtil.isBlank(dto.getIp())) {
            errorMessages.add("第" + row + "行：主IP不能为空");
        }
        if (StrUtil.isBlank(dto.getDeptName())) {
            errorMessages.add("第" + row + "行：所属部门不能为空");
        }

        // 3. 是否热设备字段标准化验证（对应原Controller第398-404行）
        if (StrUtil.isNotBlank(dto.getIsSparing())) {
            if (!("是".equals(dto.getIsSparing()) || "否".equals(dto.getIsSparing()))) {
                errorMessages.add("第" + row + "行：是否热设备应为是/否");
            }
        }

        // 4. 重要程度字典验证（对应原Controller第406-413行）
        if (StrUtil.isNotBlank(dto.getDegreeImportance())) {
            SysDictData dict = imptGradeMap.get(dto.getDegreeImportance());
            if (dict == null) {
                errorMessages.add("第" + row + "行：重要程度不存在于字典，请先在系统中配置");
            }
        }

        // 5. 部门存在性验证（对应原Controller第415-421行）
        if (StrUtil.isNotBlank(dto.getDeptName())) {
            SysDept dept = deptMap.get(dto.getDeptName());
            if (dept == null) {
                errorMessages.add("第" + row + "行：所属部门不存在，请先创建");
            }
        }

        // 6. 所在位置验证（对应原Controller第432-441行）
        if (StrUtil.isNotBlank(dto.getLocationName())) {
            TblLocation loc = locationMap.get(dto.getLocationName());
            if (loc == null) {
                errorMessages.add("第" + row + "行：所在位置不存在，请在系统中维护");
            }
        }

        // 7. 额外的数据完整性验证
        // 资产名称长度验证
        if (StrUtil.isNotBlank(dto.getAssetName()) && dto.getAssetName().length() > 256) {
            errorMessages.add("第" + row + "行：资产名称长度不能超过256个字符");
        }

        // 备注长度验证
        if (StrUtil.isNotBlank(dto.getRemark()) && dto.getRemark().length() > 512) {
            errorMessages.add("第" + row + "行：备注长度不能超过512个字符");
        }

        // 管理地址格式验证（如果有值的话）
        if (StrUtil.isNotBlank(dto.getMangerAddress()) && dto.getMangerAddress().length() > 255) {
            errorMessages.add("第" + row + "行：管理地址长度不能超过255个字符");
        }

        // 用途描述长度验证
        if (StrUtil.isNotBlank(dto.getPurpose()) && dto.getPurpose().length() > 256) {
            errorMessages.add("第" + row + "行：用途描述长度不能超过256个字符");
        }
    }

    /**
     * 业务规则验证方法 - 独立的业务逻辑验证
     * 验证数据的业务完整性和一致性
     */
    private void validateBusinessRules(TblSafetyImportDTO dto, int rowIndex, List<String> errorMessages,
                                      Map<String, Long> vendorMap, List<NetworkDomain> domains) {
        int row = rowIndex + 3;

        try {
            // 1. IP地址唯一性验证（可选，根据业务需求）
            // 这里可以添加IP地址重复检查逻辑

            // 2. 网络区域匹配验证
            if (StrUtil.isNotBlank(dto.getIp())) {
                Long domainId = getDomainIdByIp(dto.getIp(), domains);
                if (domainId == null) {
                    // 这里不强制要求匹配网络区域，只是记录警告
                    log.debug("第{}行：主IP {} 未匹配到任何网络区域，将使用默认配置", row, dto.getIp());
                } else {
                    log.debug("第{}行：主IP {} 匹配到网络区域ID：{}", row, dto.getIp(), domainId);
                }
            }
        } catch (Exception e) {
            log.error("第{}行业务规则验证异常：{}", row, e.getMessage(), e);
            errorMessages.add("第" + row + "行：业务规则验证异常 - " + e.getMessage());
        }

        // 3. 供应商验证（供应商可以自动创建，所以这里只做格式验证）
        if (StrUtil.isNotBlank(dto.getVendorName())) {
            if (dto.getVendorName().length() > 64) {
                errorMessages.add("第" + row + "行：供应商名称长度不能超过64个字符");
            }
        }

        // 4. 日期逻辑验证
        if (dto.getBuyTime() != null && dto.getMExpirationDate() != null) {
            if (dto.getBuyTime().after(dto.getMExpirationDate())) {
                errorMessages.add("第" + row + "行：购买时间不能晚于维保到期时间");
            }
        }

        // 5. 维保到期时间合理性验证
       /*  if (dto.getMExpirationDate() != null) {
            Date now = new Date();
            // 检查维保到期时间是否过于久远（比如超过20年）
            long diffYears = (dto.getMExpirationDate().getTime() - now.getTime()) / (365L * 24 * 60 * 60 * 1000);
            if (Math.abs(diffYears) > 20) {
                errorMessages.add("第" + row + "行：维保到期时间设置异常，请检查日期是否正确");
            }
        }
            */
    }

    /**
     * 数据格式验证方法 - 专门处理格式验证
     * 将格式验证逻辑独立出来，便于维护和测试
     */
    private void validateDataFormats(TblSafetyImportDTO dto, int rowIndex, List<String> errorMessages) {
        int row = rowIndex + 3;

        // 1. IP地址格式验证（更严格的验证）
        if (StrUtil.isNotBlank(dto.getIp())) {
            if (!isValidIPv4(dto.getIp())) {
                errorMessages.add("第" + row + "行：主IP地址格式不正确");
            }
        }

        // 2. 设备型号格式验证
        if (StrUtil.isNotBlank(dto.getVer()) && dto.getVer().length() > 255) {
            errorMessages.add("第" + row + "行：设备型号长度不能超过255个字符");
        }

        // 3. 设备厂家格式验证
        if (StrUtil.isNotBlank(dto.getBrandModel()) && dto.getBrandModel().length() > 256) {
            errorMessages.add("第" + row + "行：设备厂家名称长度不能超过256个字符");
        }

        // 4. 系统版本格式验证
        if (StrUtil.isNotBlank(dto.getSystemVersion()) && dto.getSystemVersion().length() > 256) {
            errorMessages.add("第" + row + "行：系统及版本信息长度不能超过256个字符");
        }
    }

    /**
     * IPv4地址格式验证（与Controller层保持一致）
     */
    private boolean isValidIPv4(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        // 使用与原Controller相同的正则表达式
        return ip.matches("^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$");
    }

    /**
     * 将DTO转换为TblSafety对象
     */
    private TblSafety buildSafetyFromDto(TblSafetyImportDTO dto, Map<String, SysDept> deptMap,
                                        Map<String, SysDictData> imptGradeMap, Map<String, TblLocation> locationMap,
                                        Map<String, Long> vendorMap, List<NetworkDomain> domains, String assetCode,Map<String, NetworkDomain> domainMap) {
        TblSafety safety = new TblSafety();

        // 基础字段设置
        safety.setAssetId(snowflake.nextId());
        safety.setAssetCode(assetCode);
        safety.setAssetName(dto.getAssetName());
        safety.setBrandModel(dto.getBrandModel());
        safety.setVer(dto.getVer());
        safety.setSystemVersion(dto.getSystemVersion());
        safety.setBuyTime(dto.getBuyTime());
        safety.setMExpirationDate(dto.getMExpirationDate());
        safety.setPurpose(dto.getPurpose());
        safety.setMangerAddress(dto.getMangerAddress());
        safety.setRemark(dto.getRemark());

        // 是否热设备标准化
        if (StrUtil.isNotBlank(dto.getIsSparing())) {
            safety.setIsSparing(Objects.equals(dto.getIsSparing(), "是") ? "1" : "0");
        }

        // 重要程度设置
        if (StrUtil.isNotBlank(dto.getDegreeImportance())) {
            SysDictData dict = imptGradeMap.get(dto.getDegreeImportance());
            if (dict != null) {
                safety.setDegreeImportance(dict.getDictValue());
            }
        }

        // 部门设置
        SysDept dept = deptMap.get(dto.getDeptName());
        if (dept != null) {
            safety.setDeptId(dept.getDeptId());
            safety.setDeptName(dto.getDeptName());
        }

        // 供应商设置（自动创建或获取）
        if (StrUtil.isNotBlank(dto.getVendorName())) {
            Long vendorId = getOrCreateVendor(dto.getVendorName(), vendorMap);
            if (vendorId != null) {
                safety.setVendor(vendorId);
                safety.setVendorName(dto.getVendorName());
            }
        }

        // 所在位置设置
        if (StrUtil.isNotBlank(dto.getLocationName())) {
            TblLocation loc = locationMap.get(dto.getLocationName());
            if (loc != null) {
                safety.setLocationId(String.valueOf(loc.getLocationId()));
                safety.setLocationFullName(dto.getLocationName());
            }
        }

        // 主IP与网络区域匹配
        Long domainId = getDomainIdByIp(dto.getIp(), domains);
        if (domainId != null) {
            safety.setDomainId(domainId);
        }
        safety.setIp(dto.getIp());

        //所属网络
        if(StrUtil.isNotBlank(dto.getDomainName())){
            NetworkDomain networkDomain = domainMap.get(dto.getDomainName());
            if (networkDomain != null){
                safety.setDomainId(networkDomain.getDomainId());
                safety.setDomainIds(networkDomain.getDomainId().toString());
                List< String> domainIds = new ArrayList<>();
                domainIds.add(networkDomain.getDomainId().toString());
                safety.setDomainIdsList(domainIds);
            }
        }


        // 基础审计字段（使用当前用户信息）
        safety.setCreateBy("system"); // 可以根据需要获取当前用户
        safety.setUserId(1L); // 可以根据需要获取当前用户ID
        safety.setDeptId(safety.getDeptId() != null ? safety.getDeptId() : 1L);
        safety.setUpdateTime(DateUtil.date());
        safety.setState("0"); // 默认离线

        return safety;
    }

    /**
     * 获取或创建供应商
     */
    private Long getOrCreateVendor(String vendorName, Map<String, Long> vendorMap) {
        if (StrUtil.isBlank(vendorName)) return null;

        Long cached = vendorMap.get(vendorName);
        if (cached != null) return cached;

        // 查询数据库
        TblVendor query = new TblVendor();
        query.setVendorName(vendorName);
        List<TblVendor> list = tblVendorService.selectTblVendorList(query);
        if (CollUtil.isNotEmpty(list)) {
            Long id = list.get(0).getId();
            vendorMap.put(vendorName, id);
            return id;
        }

        // 自动创建新供应商
        TblVendor nv = new TblVendor();
        nv.setVendorName(vendorName);
        nv.setVendorCode("AUTO_" + System.currentTimeMillis());
        nv.setCreateBy("system");
        nv.setUserId(1L);
        nv.setDeptId(1L);
        int rs = tblVendorService.insertTblVendor(nv);
        if (rs > 0) {
            vendorMap.put(vendorName, nv.getId());
            return nv.getId();
        }
        return null;
    }

    /**
     * 根据IP匹配网络区域（CIDR）
     */
    private Long getDomainIdByIp(String ip, List<NetworkDomain> domains) {
        if (StrUtil.isBlank(ip) || CollUtil.isEmpty(domains)) return null;
        try {
            for (NetworkDomain d : domains) {
                if (d != null && StrUtil.isNotBlank(d.getIparea())) {
                    try {
                        if(IpUtils.isInRange(ip, d.getIparea())){
                            log.debug("IP地址 {} 匹配到网络区域：{} ({})", ip, d.getDomainName(), d.getIparea());
                            return d.getDomainId();
                        }
                    } catch (Exception ignore) {}
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 批量保存安全设备数据
     * 确保主表数据和关联表数据的一致性保存
     *
     * @param validSafeties 验证通过的安全设备列表
     * @throws Exception 保存异常
     */
    private void batchSaveSafetyData(List<TblSafety> validSafeties) throws Exception {
        if (CollUtil.isEmpty(validSafeties)) {
            log.warn("批量保存：没有需要保存的安全设备数据");
            return;
        }

        log.info("开始批量保存安全设备数据，数量：{}", validSafeties.size());

        int successCount = 0;
        int failureCount = 0;
        List<String> failureDetails = new ArrayList<>();
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < validSafeties.size(); i++) {
            TblSafety safety = validSafeties.get(i);
            try {
                log.debug("开始保存第{}条数据：资产名称={}, IP={}, 资产编码={}",
                         (i + 1), safety.getAssetName(), safety.getIp(), safety.getAssetCode());

                // 使用现有的insertTblSafety方法，确保关联表数据一致性
                // 该方法包含以下关联表处理：
                // 1. TblNetworkIpMac - IP-MAC关联表
                // 2. TblAssetOverview - 资产总览表
                // 3. 在线检测任务
                int result = insertTblSafety(safety);

                if (result > 0) {
                    successCount++;
                    log.debug("第{}条安全设备数据保存成功：资产名称={}, IP={}, 资产ID={}",
                             (i + 1), safety.getAssetName(), safety.getIp(), safety.getAssetId());
                } else {
                    failureCount++;
                    String errorMsg = String.format("第%d条数据保存失败：%s (%s) - 数据库返回0",
                                                   (i + 1), safety.getAssetName(), safety.getIp());
                    failureDetails.add(errorMsg);
                    log.error(errorMsg);
                }

            } catch (Exception e) {
                failureCount++;
                String errorMsg = String.format("第%d条数据保存异常：%s (%s) - %s",
                                               (i + 1), safety.getAssetName(), safety.getIp(), e.getMessage());
                failureDetails.add(errorMsg);
                log.error("保存安全设备数据异常：资产名称={}, IP={}, 异常详情={}",
                         safety.getAssetName(), safety.getIp(), e.getMessage(), e);

                // 在批量保存过程中，如果遇到异常，抛出ServiceException触发事务回滚
                throw new ServiceException("批量保存安全设备数据失败：" + errorMsg);
            }
        }

        // 记录批量保存结果和性能统计
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        log.info("批量保存安全设备数据完成 - 成功：{}条，失败：{}条，耗时：{}ms，平均：{}ms/条",
                successCount, failureCount, duration,
                validSafeties.size() > 0 ? duration / validSafeties.size() : 0);

        // 如果有失败的记录，记录详细信息
        if (failureCount > 0) {
            log.error("批量保存失败详情：{}", String.join("; ", failureDetails));
            throw new ServiceException("批量保存过程中有" + failureCount + "条数据保存失败");
        }
    }


}
