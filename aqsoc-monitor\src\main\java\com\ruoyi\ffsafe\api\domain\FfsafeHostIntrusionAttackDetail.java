package com.ruoyi.ffsafe.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 主机入侵攻击详情对象 ffsafe_host_intrusion_attack_detail
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
public class FfsafeHostIntrusionAttackDetail {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 关联主表ID */
    @Excel(name = "攻击事件ID")
    private Long attackId;

    /** 详情类型: brute_force=暴力破解,web_attack=Web攻击,vuln_scan=漏洞扫描 */
    @Excel(name = "详情类型", readConverterExp = "brute_force=暴力破解,web_attack=Web攻击,vuln_scan=漏洞扫描")
    private String detailType;

    /** 详情数据JSON格式 */
    private String detailData;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 获取详情类型文本
     */
    public String getDetailTypeText() {
        if (detailType == null) {
            return "未知";
        }
        switch (detailType) {
            case "brute_force":
                return "暴力破解";
            case "web_attack":
                return "Web攻击";
            case "vuln_scan":
                return "漏洞扫描";
            default:
                return "其他";
        }
    }

    /**
     * 设置详情类型
     */
    public void setDetailTypeByText(String detailTypeText) {
        if ("暴力破解".equals(detailTypeText)) {
            this.detailType = "brute_force";
        } else if ("Web攻击".equals(detailTypeText)) {
            this.detailType = "web_attack";
        } else if ("漏洞扫描".equals(detailTypeText)) {
            this.detailType = "vuln_scan";
        } else {
            this.detailType = "other";
        }
    }
}
