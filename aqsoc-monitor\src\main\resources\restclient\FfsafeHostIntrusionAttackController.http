### 查询主机入侵攻击列表
GET {{baseUrl}}/ffsafe/hostIntrusionAttack/list?dip=&sip=&dipName=&alertName=&handleState=&deviceConfigId=&disposer=&ffId=
Authorization: Bearer {{token}}
Content-Type: application/json

### 导出主机入侵攻击列表
POST {{baseUrl}}/ffsafe/hostIntrusionAttack/export
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "dip": "",
  "sip": "",
  "dipName": "",
  "alertName": "",
  "handleState": 0,
  "deviceConfigId": 0,
  "disposer": "",
  "ffId": 0
}

### 获取主机入侵攻击详细信息
GET {{baseUrl}}/ffsafe/hostIntrusionAttack/1
Authorization: Bearer {{token}}

### 修改主机入侵攻击
PUT {{baseUrl}}/ffsafe/hostIntrusionAttack
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": 1,
  "ffId": 1001,
  "sip": "*************",
  "dip": "*************",
  "dipName": "web-server-01",
  "alertName": "可疑进程创建",
  "startTime": "2023-01-01 10:30:00",
  "updateTime": "2023-01-01 10:35:00",
  "alertDetail": "检测到异常进程创建行为",
  "handleState": 0,
  "handleDesc": "",
  "disposer": "",
  "deviceConfigId": 10
}

### 删除单个主机入侵攻击
DELETE {{baseUrl}}/ffsafe/hostIntrusionAttack/1
Authorization: Bearer {{token}}

### 批量删除主机入侵攻击
DELETE {{baseUrl}}/ffsafe/hostIntrusionAttack/batch/1,2,3
Authorization: Bearer {{token}}

### 统计满足查询条件的记录总数
GET {{baseUrl}}/ffsafe/hostIntrusionAttack/statistics?dip=&sip=&dipName=&alertName=&handleState=&deviceConfigId=&disposer=&ffId=
Authorization: Bearer {{token}}

### 单个处置攻击事件
POST {{baseUrl}}/ffsafe/hostIntrusionAttack/handle
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": 1,
  "handleState": 1,
  "handleDesc": "已确认为误报，已处理"
}

### 批量处置攻击事件
POST {{baseUrl}}/ffsafe/hostIntrusionAttack/batchHandle
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "eventIds": [
    1,
    2,
    3
  ],
  "handleState": 1,
  "handleDesc": "批量处理完成"
}

### 根据attackId查询攻击详情
GET {{baseUrl}}/ffsafe/hostIntrusionAttack/detail/1
Authorization: Bearer {{token}}