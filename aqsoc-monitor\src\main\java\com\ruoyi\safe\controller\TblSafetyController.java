package com.ruoyi.safe.controller;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.web.util.matcher.IpAddressMatcher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.domain.TblLocation;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.dict.service.ITblLocationService;
import com.ruoyi.safe.aspectj.AssetAction;
import com.ruoyi.safe.aspectj.AssetDataHandle;
import com.ruoyi.safe.countByDict.service.ICountByDictService;
import com.ruoyi.safe.domain.TblAssetOverview;
import com.ruoyi.safe.domain.TblSafety;
import com.ruoyi.safe.domain.TblSafetyTemplate;
import com.ruoyi.safe.domain.TblVendor;
import com.ruoyi.safe.domain.dto.QueryDeptSafetyCountDto;
import com.ruoyi.safe.dto.TblSafetyImportDTO;
import com.ruoyi.safe.service.IAssetVulnerabilityStatsService;
import com.ruoyi.safe.service.IMonitorHandleService;
import com.ruoyi.safe.service.ITblAssetOverviewService;
import com.ruoyi.safe.service.ITblBusinessApplicationService;
import com.ruoyi.safe.service.ITblSafetyService;
import com.ruoyi.safe.service.ITblVendorService;
import com.ruoyi.safe.vo.countDict.CountByDictVO;
import com.ruoyi.safe.vo.countDict.CountDictTypeVO;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 安全设备Controller
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
@RestController
@RequestMapping("/safe/safety")
public class TblSafetyController extends BaseController
{
    @Autowired
    private ITblSafetyService tblSafetyService;

    @Autowired
    private ICountByDictService countByDictService;

    @Autowired
    private IMonitorHandleService monitorHandleService;

    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private INetworkDomainService networkDomainService;
    @Autowired
    private Snowflake snowflake;
    @Autowired
    private ITblAssetOverviewService tblAssetOverviewService;
    @Autowired
    private IAssetVulnerabilityStatsService assetVulnerabilityStatsService;
    @Autowired
    private ITblLocationService tblLocationService;
    @Autowired
    private ITblVendorService tblVendorService;
    @Autowired
    private ITblBusinessApplicationService tblBusinessApplicationService;
    @Autowired
    private com.ruoyi.safe.mapper.TblAssetOverviewMapper tblAssetOverviewMapper;

    private static final String IPV4_REGEX =
            "^(?:(?:\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])\\.){3}(?:\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])$";

    /**
     * 查询安全设备列表
     */
    //@PreAuthorize("@ss.hasPermi('safe:safety:list')")
    @DataScope(deptAlias = "a", userAlias = "a")
    @AssetDataHandle(action = AssetAction.SELECT)
    @GetMapping("/list")
    public TableDataInfo list(TblSafety tblSafety)
    {
        startPage();
        List<TblSafety> list = tblSafetyService.selectTblSafetyList(tblSafety);
        if(CollUtil.isNotEmpty( list)){
            List<JSONObject> assetFieldsItemList = tblBusinessApplicationService.selectAssetFieldsItemList("5");
            if (CollUtil.isNotEmpty(assetFieldsItemList)){
                List<JSONObject> basicInformationList = assetFieldsItemList.stream()
                        .filter(jsonObject -> "基本信息".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                for (int i = 0; i < list.size(); i++) {
                    TblSafety safety = list.get(i);
                    int denominator = assetFieldsItemList.size();
                    AtomicReference<Integer> numerator = new AtomicReference<>(0);
                    if (CollUtil.isNotEmpty(basicInformationList)){
                        for (JSONObject jsonObject : basicInformationList){
                            String fieldKey = jsonObject.getString("fieldKey");
                            Object fieldValue = ReflectUtil.getFieldValue(safety, fieldKey);
                            if (fieldValue != null && !"".equals(fieldValue)){
                                numerator.getAndSet(numerator.get() + 1);
                            }
                        }
                    }
                    double completeness = ((double) numerator.get() / denominator) * 100;
                    BigDecimal bd = new BigDecimal(completeness).setScale(2, RoundingMode.DOWN);
                    safety.setCompletenessStr(bd.toString());
                    safety.setCompleteness(bd);
                }
            }
        }

        // 为安全设备列表添加统计信息
        assetVulnerabilityStatsService.batchEnrichSafetyWithStats(list);

        return getDataTable(list);
    }

    /**
     * 查询安全设备列表（不分页）
     * */
    @GetMapping("/selectSafetyList")
    public TableDataInfo selectSafetyList(TblSafety tblSafety)
    {
        List<TblSafety> list = tblSafetyService.selectTblSafetyList(tblSafety);
        return getDataTable(list);
    }

    /**
     * 导出安全设备列表
     */
    @PreAuthorize("@ss.hasPermi('safe:safety:export')")
    @Log(title = "安全设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblSafety tblSafety)
    {
        List<TblSafety> list = tblSafetyService.selectTblSafetyList(tblSafety);
        list.forEach(e -> {
            if (StringUtils.isNotBlank(e.getLaseScanState())) {
                e.setState(e.getLaseScanState());
            }
        });
        ExcelUtil<TblSafety> util = new ExcelUtil<TblSafety>(TblSafety.class);
        util.exportExcel(response, list, "安全设备数据");
    }

    /**
     * 获取安全设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('safe:safety:query')")
    @GetMapping(value = "/{assetId}")
    public AjaxResult getInfo(@PathVariable("assetId") Long assetId)
    {
        return AjaxResult.success(tblSafetyService.selectTblSafetyByAssetId(assetId));
    }

    /**
     * 新增安全设备
     */
    @PreAuthorize("@ss.hasPermi('safe:safety:add')")
    @Log(title = "安全设备", businessType = BusinessType.INSERT)
    @AssetDataHandle(action = AssetAction.INSERT)
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody TblSafety tblSafety)
    {
        if (StrUtil.isNotBlank(tblSafety.getIp())){
            List<String> ips = StrUtil.split(tblSafety.getIp(),",");
            monitorHandleService.deleteByIp(ips);
        }
        AjaxResult ajaxResult = toAjax(tblSafetyService.insertTblSafety(tblSafety));
        ajaxResult.put("data",tblSafety);
        return ajaxResult;
    }

    /**
     * 修改安全设备
     */
    @PreAuthorize("@ss.hasPermi('safe:safety:edit')")
    @Log(title = "安全设备", businessType = BusinessType.UPDATE)
    @AssetDataHandle(action = AssetAction.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblSafety tblSafety)
    {
        return toAjax(tblSafetyService.updateTblSafety(tblSafety));
    }

    /**
     * 删除安全设备
     */
    @PreAuthorize("@ss.hasPermi('safe:safety:remove')")
    @Log(title = "安全设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{assetIds}")
    public AjaxResult remove(@PathVariable Long[] assetIds)
    {
        return toAjax(tblSafetyService.deleteTblSafetyByAssetIds(assetIds));
    }

    /**
     * 根据不同的字典类型统计安全设备数量
     * @param dictType
     * @return
     */
    @GetMapping("/getSafetyCountByDict")
    public AjaxResult getSafetyCountByDict(String dictType) {
        CountDictTypeVO countByDict = countByDictService.getCountByDict(dictType,"safety");
        return AjaxResult.success(countByDict);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TblSafetyTemplate> util = new ExcelUtil<TblSafetyTemplate>(TblSafetyTemplate.class);
        List<TblSafetyTemplate> list = new ArrayList<>();
        TblSafetyTemplate tblSafetyTemlate = new TblSafetyTemplate();
        tblSafetyTemlate.setAssetName("例如：XX安全设备");
        tblSafetyTemlate.setIp("例如：***********");
        tblSafetyTemlate.setAssetCode("例如：AQSB112323");
        tblSafetyTemlate.setDeptName("例如：XX部门");
        tblSafetyTemlate.setDomainName("例如：XX网络区域");
        tblSafetyTemlate.setVendorName("例如：XX供应商");
        tblSafetyTemlate.setDegreeImportance("例如：非常重要/重要/一般/不太重要/不重要");
        tblSafetyTemlate.setLocationFullName("例如：XX机房");
        tblSafetyTemlate.setPurpose("例如：用于XXX");
        tblSafetyTemlate.setBrandModel("例如：XX工厂");
        tblSafetyTemlate.setSystemVersion("例如：XX系统版本");
        tblSafetyTemlate.setIsVirtual("例如：是/否");
        tblSafetyTemlate.setAssetTypeName("例如：漏洞扫描");
        tblSafetyTemlate.setVer("例如：XX版本");
        tblSafetyTemlate.setRemark("例如：XX备注");
        tblSafetyTemlate.setLocationDetail("例如：江西省/南昌市/xxx/xxx");
        tblSafetyTemlate.setBuyTime(new Date());
        tblSafetyTemlate.setMExpirationDate(new Date());
        tblSafetyTemlate.setIsSparing("例如：是/否");
        tblSafetyTemlate.setMangerAddress("例如：XX地址");
        list.add(tblSafetyTemlate);
        util.exportExcel(response,list,"安全设备模板");
    }

    /**
     * 下载 安全设备 简化导入模板
     */
    @PostMapping("/importJtTemplate")
    public void importJtTemplate(HttpServletResponse response) {
        ExcelUtil<TblSafetyImportDTO> util = new ExcelUtil<TblSafetyImportDTO>(TblSafetyImportDTO.class);
        List<TblSafetyImportDTO> list = new ArrayList<>();
        TblSafetyImportDTO dto = new TblSafetyImportDTO();
        dto.setAssetName("例如：XX安全设备");
        dto.setBrandModel("例如：XX厂家");
        dto.setVer("例如：XX型号");
        dto.setSystemVersion("例如：XX系统及版本");
        dto.setBuyTime(new Date());
        dto.setMExpirationDate(new Date());
        dto.setIp("例如：***********");
        dto.setPurpose("例如：用于XXX");
        dto.setMangerAddress("例如：XX管理地址");
        dto.setDegreeImportance("例如：非常重要/重要/一般/不太重要/不重要");
        dto.setIsSparing("是/否");
        dto.setLocationName("例如：XX机房");
        dto.setDeptName("例如：XX部门");
        dto.setDomainName("例如：XX网络区域");
        dto.setVendorName("例如：XX供应商");
        dto.setRemark("例如：XX备注");
        list.add(dto);
        util.exportExcel(response, list, "安全设备导入模板", "注意事项：\n" +
                "1. 字段前有*则属于必填项，如未填写则无法导入，其余为非必填选项。\n" +
                "2. 以下字段需参考系统“选项数据”填写：所属部门、重要程度、供应商、所在位置，否则无法导入或会自动新建供应商。\n" +
                "3. 此模板为简化版本，仅包含核心导入字段。");
    }

    /**
     * 简化导入数据（基于TblSafetyImportDTO）
     * 重构后的方法，将复杂业务逻辑委托给Service层处理
     */
    @Log(title = "安全设备", businessType = BusinessType.IMPORT)
    @PostMapping("/importJtData")
    @PreAuthorize("@ss.hasPermi('safe:safety:import')")
    public AjaxResult importJtData(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("上传文件为空");
        }
        try (InputStream inputStream = file.getInputStream()) {
            ExcelUtil<TblSafetyImportDTO> util = new ExcelUtil<>(TblSafetyImportDTO.class);
            // 跳过标题行（导出模板包含“注意事项”标题），避免表头错位导致对象为null
            List<TblSafetyImportDTO> dtoList = util.importExcel(inputStream, 1);
            // 过滤空行与null对象，保留至少有一个关键字段的行
            dtoList = Optional.ofNullable(dtoList).orElse(Collections.emptyList()).stream()
                    .filter(Objects::nonNull)
                    .filter(d -> StrUtil.isNotBlank(d.getAssetName()) || StrUtil.isNotBlank(d.getIp()) || StrUtil.isNotBlank(d.getDeptName()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(dtoList)) {
                return AjaxResult.error("导入数据为空或模板表头不匹配，请使用“下载导入模板”重新填写后再导入");
            }

            // 基本必填字段验证
            for (int i = 0; i < dtoList.size(); i++) {
                TblSafetyImportDTO dto = dtoList.get(i);

                // 必填字段验证
                if (StringUtils.isBlank(dto.getAssetName())) {
                    return AjaxResult.error("第" + (i + 3) + "行：资产名称不能为空");
                }
                if (StringUtils.isBlank(dto.getIp())) {
                    return AjaxResult.error("第" + (i + 3) + "行：IP地址不能为空");
                }
                if (StringUtils.isBlank(dto.getDeptName())) {
                    return AjaxResult.error("第" + (i + 3) + "行：所属部门不能为空");
                }
            }

            // 调用Service层方法处理业务逻辑
            String message = tblSafetyService.importSafetyFromTemplate(dtoList);
            return AjaxResult.success(message);
        } catch (ServiceException e) {
            logger.error("安全设备数据导入失败: {}", e.getMessage(), e);
            // 统一文案前缀在控制器层处理一次
            return AjaxResult.error("导入失败：" + e.getMessage());
        } catch (Exception e) {
            logger.error("安全设备数据导入发生系统异常", e);
            return AjaxResult.error("系统内部异常，请联系管理员");
        }
    }

    @PostMapping("/importData")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("上传文件为空");
        }

        try (InputStream inputStream = file.getInputStream()) {
            ExcelUtil<TblSafetyTemplate> util = new ExcelUtil<>(TblSafetyTemplate.class);
            List<TblSafetyTemplate> list = util.importExcel(inputStream);

            if (CollUtil.isEmpty(list)) {
                return AjaxResult.error("导入数据为空");
            }

            // 提前加载并转换为Map以提高查询效率
            Map<String, SysDept> deptMap = deptService.selectDeptList(new SysDept())
                    .stream()
                    .collect(Collectors.toMap(SysDept::getDeptName, dept -> dept));
            SysDictData sysDictData = new SysDictData();
            sysDictData.setDictType("impt_grade");
            Map<String, SysDictData> imptGradeMap = dictDataService.selectDictDataList(sysDictData)
                    .stream()
                    .collect(Collectors.toMap(SysDictData::getDictLabel, data -> data));

            CountDictTypeVO assetTypeData = countByDictService.getCountByDict("asset_type", "safety");
            Map<String, CountByDictVO> assetTypeMap = Optional.ofNullable(assetTypeData.getCountByDictList())
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.toMap(CountByDictVO::getDictLabel, vo -> vo));

            Map<String, NetworkDomain> networkDomainMap = networkDomainService.selectNetworkDomainList(new NetworkDomain())
                    .stream()
                    .collect(Collectors.toMap(NetworkDomain::getDomainName, domain -> domain));
            Map<String, TblLocation> vendorLocationMap = tblLocationService.selectTblLocationList(new TblLocation())
                    .stream().collect(Collectors.toMap(TblLocation::getLocationFullName, location -> location));
            Map<String, TblVendor> vendorMap = tblVendorService.selectTblVendorList(new TblVendor())
                    .stream().collect(Collectors.toMap(TblVendor::getVendorName, vendor -> vendor));

            int successCount = 0;
            for (int index = 1; index <= list.size(); index++) {
                TblSafetyTemplate tblSafetyTemplate = list.get(index - 1);

                // 校验字段
                checkField(tblSafetyTemplate.getAssetName(), "资产名称", index);
                checkField(tblSafetyTemplate.getIp(), "IP地址", index);
                checkField(tblSafetyTemplate.getDeptName(), "所属部门", index);
                checkField(tblSafetyTemplate.getDomainName(), "所属网络", index);
                checkField(tblSafetyTemplate.getAssetTypeName(), "资产类型", index);
                checkField(tblSafetyTemplate.getDegreeImportance(), "重要程度", index);

                // 校验IP地址
                if (!isValidIPv4(tblSafetyTemplate.getIp())) {
                    throw new ServiceException("第" + index + "行, IP地址格式不正确");
                }

                // 自动生成资产编码
                if (StrUtil.isBlank(tblSafetyTemplate.getAssetCode())){
                    tblSafetyTemplate.setAssetCode("AQSB"+ DateUtil.format(new Date(), "yyyyMMddHHmmss"+ successCount));
                }

                if (StrUtil.isNotBlank(tblSafetyTemplate.getIsSparing())){
                    if ("是".equals(tblSafetyTemplate.getIsSparing())){
                        tblSafetyTemplate.setIsSparing("是");
                    }else if ("否".equals(tblSafetyTemplate.getIsSparing())){
                        tblSafetyTemplate.setIsSparing("否");
                    }else {
                        throw new ServiceException("第" + index + "行, 停用状态填写错误");
                    }
                }

                // 校验供应商
                TblVendor tblVendor = vendorMap.get(tblSafetyTemplate.getVendorName());
                if (tblVendor == null) {
                    throw new ServiceException("第" + index + "行, 供应商不存在");
                }
                tblSafetyTemplate.setVendor(tblVendor.getId());

                //校验资产位置
                TblLocation tblLocation = vendorLocationMap.get(tblSafetyTemplate.getLocationFullName());
                if (tblLocation == null) {
                    /*throw new ServiceException("第" + index + "行, 所在位置不存在");*/
                    tblSafetyTemplate.setLocationId(null);
                }else {
                    tblSafetyTemplate.setLocationId(tblLocation.getLocationId().toString());
                }

                //校验虚拟设备
                if (StrUtil.isNotBlank(tblSafetyTemplate.getIsVirtual())){
                    if ("是".equals(tblSafetyTemplate.getIsVirtual())){
                        tblSafetyTemplate.setIsVirtual("Y");
                    }else if ("否".equals(tblSafetyTemplate.getIsVirtual())){
                        tblSafetyTemplate.setIsVirtual("N");
                    }else {
                        throw new ServiceException("第" + index + "行, 虚拟设备填写有误");
                    }
                }

                // 校验部门
                SysDept dept = deptMap.get(tblSafetyTemplate.getDeptName());
                if (dept == null) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblSafetyTemplate.getDeptName() + " 部门不存在");
                }
                tblSafetyTemplate.setDeptId(dept.getDeptId());

                // 校验网络区域
                NetworkDomain networkDomain = networkDomainMap.get(tblSafetyTemplate.getDomainName());
                if (networkDomain == null) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblSafetyTemplate.getDomainName() + " 网络区域不存在");
                    //tblSafetyTemplate.setDomainId(null);
                }else {
                    tblSafetyTemplate.setDomainId(networkDomain.getDomainId());
                }

                // 校验重要程度类型
                SysDictData imptGrade = imptGradeMap.get(tblSafetyTemplate.getDegreeImportance());
                if (imptGrade == null) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblSafetyTemplate.getDegreeImportance() + " 重要程度不存在");
                }
                tblSafetyTemplate.setDegreeImportance(imptGrade.getDictValue());

                // 校验资产类型
                CountByDictVO assetType = assetTypeMap.get(tblSafetyTemplate.getAssetTypeName());
                if (assetType == null) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblSafetyTemplate.getAssetTypeName() + " 资产类型不存在");
                }
                tblSafetyTemplate.setAssetType(Long.parseLong(assetType.getDictValue()));

                //校验资产编码是否唯一
                TblAssetOverview tblAssetOverview = new TblAssetOverview();
                tblAssetOverview.setAssetCode(tblSafetyTemplate.getAssetCode());
                if (com.ruoyi.common.utils.StringUtils.isNotEmpty(tblSafetyTemplate.getAssetCode()) && !tblAssetOverviewService.checkAssetCodeUnique(tblAssetOverview)) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblSafetyTemplate.getAssetCode() + "资产编码已存在");
                }
                // 插入数据
                TblSafety tblSafety = new TblSafety();
                BeanUtils.copyProperties(tblSafetyTemplate, tblSafety);
                tblSafety.setAssetId(snowflake.nextId());
                if (tblSafety.getUserId() == null) {
                    tblSafety.setUserId(getUserId());
                }
                if (tblSafety.getDeptId() == null) {
                    tblSafety.setDeptId(getDeptId());
                }
                tblSafetyService.insertTblSafety(tblSafety);

                successCount++;
            }

            logger.info("成功导入 {} 行数据", successCount);
            return AjaxResult.success("成功导入 " + successCount + " 行数据");
        } catch (IOException e) {
            logger.error("文件读取失败: {}", e.getMessage(), e);
            throw new ServiceException("文件读取失败: " + e.getMessage());
        } catch (ServiceException e) {
            logger.error("数据校验失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("未知错误: {}", e.getMessage(), e);
            throw new ServiceException("数据导入错误: " + e.getMessage());
        }
    }

    private void checkField(String value, String fieldName,int index) throws Exception {
        if (StrUtil.isBlank(value)) {
            throw new Exception("导入失败:第"+index+"行,"+ fieldName + "不能为空");
        }
    }

    public static boolean isValidIPv4(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        return ip.matches(IPV4_REGEX);
    }

    /**
     * 获取部门安全设备统计
     */
    @GetMapping("/getDepts")
    public AjaxResult getDepts() {
        QueryDeptSafetyCountDto queryCountDto = new QueryDeptSafetyCountDto();
        SysDept sysDept = new SysDept();
        sysDept.setDeptId(getDeptId());
        queryCountDto.setSysDept(sysDept);
        return AjaxResult.success(tblSafetyService.getDeptSafetyCount(queryCountDto));
    }
}
