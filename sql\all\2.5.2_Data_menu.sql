use aqsoc;

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (700749082984972695, '设备接入配置', 2602, 0, 'deviceConfig', 'monitor/deviceConfig/index', NULL, 1, 0, 'C', '0', '0', 'api:deviceConfig:list', '#', 'admin', '2025-08-13 17:49:40', 'admin', '2025-08-13 17:54:49', '设备接入配置菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (700749082984972696, '设备接入配置查询', 700749082984972695, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'api:deviceConfig:query', '#', 'admin', '2025-08-13 17:49:45', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (700749082984972697, '设备接入配置新增', 700749082984972695, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'api:deviceConfig:add', '#', 'admin', '2025-08-13 17:49:45', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (700749082984972698, '设备接入配置修改', 700749082984972695, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'api:deviceConfig:edit', '#', 'admin', '2025-08-13 17:49:45', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (700749082984972699, '设备接入配置删除', 700749082984972695, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'api:deviceConfig:remove', '#', 'admin', '2025-08-13 17:49:45', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (700749082984972700, '设备接入配置导出', 700749082984972695, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'api:deviceConfig:export', '#', 'admin', '2025-08-13 17:49:45', '', NULL, '');
