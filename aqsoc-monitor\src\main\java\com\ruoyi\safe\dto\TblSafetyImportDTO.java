package com.ruoyi.safe.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 * 安全设备简化导入DTO
 * 仅包含导入所需核心字段，Excel列名严格按照产品要求定义
 */
@Data
public class TblSafetyImportDTO implements Serializable {

    /** 资产名称（必填） */
    @NotBlank(message = "资产名称不能为空")
    @Excel(name = "*资产名称", sort = 1)
    private String assetName;

    /** 设备厂家（选填） */
    @Excel(name = "设备厂家", sort = 2)
    private String brandModel;

    /** 设备型号（选填） */
    @Excel(name = "设备型号", sort = 3)
    private String ver;

    /** 系统及版本（选填） */
    @Excel(name = "系统及版本", sort = 4)
    private String systemVersion;

    /** 购买时间（选填） */
    @Excel(name = "购买时间", sort = 5, width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date buyTime;

    /** 维保到期时间（选填） */
    @Excel(name = "维保到期时间", sort = 6, width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date mExpirationDate;

    /** 主IP（必填） */
    @NotBlank(message = "主IP不能为空")
    @Pattern(regexp = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$",
            message = "主IP格式不正确")
    @Excel(name = "*主IP", sort = 7)
    private String ip;

    /** 用途（选填） */
    @Excel(name = "用途", sort = 8)
    private String purpose;

    /** 管理地址（选填） */
    @Excel(name = "管理地址", sort = 9)
    private String mangerAddress;

    /** 重要程度（选填，但当提供时需存在于字典impt_grade） */
    @Excel(name = "重要程度", sort = 10)
    private String degreeImportance;

    /** 是否热设备（选填，值：是/否） */
    @Excel(name = "是否热设备", sort = 11)
    private String isSparing;

    /** 所在位置（选填） */
    @Excel(name = "所在位置", sort = 12)
    private String locationName;

    /** 所属部门（必填） */
    @NotBlank(message = "所属部门不能为空")
    @Excel(name = "*所属部门", sort = 13)
    private String deptName;

    @Excel(name = "所属网络区域",sort = 14)
    private String domainName;

    /** 供应商（选填，若不存在将自动创建） */
    @Excel(name = "供应商", sort = 15)
    private String vendorName;

    /** 备注（选填） */
    @Excel(name = "备注", sort = 16)
    private String remark;
}

