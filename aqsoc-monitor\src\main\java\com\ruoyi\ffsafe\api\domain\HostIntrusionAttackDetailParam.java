package com.ruoyi.ffsafe.api.domain;

import cn.hutool.extra.spring.SpringUtil;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.ffsafe.scantaskapi.domain.ParamBase;
import com.ruoyi.ffsafe.scantaskapi.domain.RequestBase;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;

import java.nio.charset.StandardCharsets;

/**
 * 主机入侵攻击详情接口参数类
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@Slf4j
public class HostIntrusionAttackDetailParam extends ParamBase implements RequestBase {

    /** 访问令牌 */
    private String accessToken;
    
    /** 数据ID，可在主机入侵事件响应中获取 */
    private Integer id;
    
    /** 设备配置ID */
    private Long deviceConfigId;

    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        TblDeviceConfig deviceConfig = FfsafeClientService.deviceConfigThreadLocal.get();
        ITblDeviceConfigService deviceConfigService = SpringUtil.getBean(ITblDeviceConfigService.class);
        FfsafeApiConfig ffsafeApiConfig = deviceConfigService.getFfsafeApiConfig(deviceConfig);
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();
        if (ffurl == null && !updateFfsafeApiConfig(deviceId)) {
            return null;
        }

        // 构建请求URL
        String url = ffurl + "/v2/host-edr-intrusion/detail";

        // 创建POST请求
        HttpPost httpPost = new HttpPost(url);

        // 设置请求头
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");

        // 构建请求体参数
        StringBuilder params = new StringBuilder();
        params.append("access_token=").append(fftoken);
        params.append("&id=").append(id);

        // 设置请求体
        StringEntity entity = new StringEntity(params.toString(), StandardCharsets.UTF_8);
        httpPost.setEntity(entity);

        log.info("非凡API主机入侵攻击详情请求参数: URL={}, Params={}", url, params.toString());

        return httpPost;
    }
}
